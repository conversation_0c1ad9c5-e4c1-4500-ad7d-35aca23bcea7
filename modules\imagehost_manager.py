#!/usr/bin/env python3
"""
图床管理模块 - 支持GitHub图床
"""

import os
import base64
import requests
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from .logger import Logger

class ImageHostManager:
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = Logger()
        self.history_file = "imagehost_history.json"
        self._ensure_history_file()
        
    def get_imagehost_config(self) -> Dict[str, Any]:
        """获取图床配置"""
        try:
            # 直接从配置管理器的内部配置获取
            return self.config_manager.config.get("imagehost", {
                "github_token": "",
                "repo_path": "/scys-wcs/photo/contents/image",
                "max_file_size": 3
            })
        except Exception as e:
            self.logger.log(f"Error loading imagehost config: {str(e)}", "ERROR")
            return {
                "github_token": "",
                "repo_path": "/scys-wcs/photo/contents/image",
                "max_file_size": 3
            }
    
    def save_imagehost_config(self, config_data: Dict[str, Any]) -> bool:
        """保存图床配置"""
        try:
            # 更新配置管理器的内部配置
            self.config_manager.config["imagehost"] = {
                "github_token": config_data.get("github_token", ""),
                "repo_path": config_data.get("repo_path", "/scys-wcs/photo/contents/image"),
                "max_file_size": int(config_data.get("max_file_size", 3))
            }

            # 保存配置
            self.config_manager.save_config()
            self.logger.log("ImageHost config saved successfully")
            return True

        except Exception as e:
            self.logger.log(f"Error saving imagehost config: {str(e)}", "ERROR")
            return False
    
    def validate_file_size(self, file_size: int, max_size_mb: int) -> bool:
        """验证文件大小"""
        max_size_bytes = max_size_mb * 1024 * 1024
        return file_size <= max_size_bytes
    
    def generate_filename(self, original_filename: str) -> str:
        """生成文件名"""
        # 获取文件扩展名
        _, ext = os.path.splitext(original_filename)
        if not ext:
            ext = '.png'
        
        # 生成时间戳文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{timestamp}{ext}"
    
    def upload_to_github(self, file_content: bytes, filename: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """上传文件到GitHub"""
        try:
            # 解析仓库路径
            repo_path = config.get("repo_path", "/scys-wcs/photo/contents/image")
            if not repo_path.startswith("/"):
                repo_path = "/" + repo_path
            
            # 解析路径: /username/repo/contents/path
            path_parts = repo_path.strip("/").split("/")
            if len(path_parts) < 4 or path_parts[2] != "contents":
                raise ValueError("仓库路径格式错误，应为: /username/repo/contents/path")
            
            username = path_parts[0]
            repo = path_parts[1]
            file_path = "/".join(path_parts[3:]) + "/" + filename
            
            # GitHub API URL
            api_url = f"https://api.github.com/repos/{username}/{repo}/contents/{file_path}"
            
            # 编码文件内容
            content_base64 = base64.b64encode(file_content).decode('utf-8')
            
            # 请求头
            headers = {
                "Authorization": f"token {config.get('github_token')}",
                "Accept": "application/vnd.github.v3+json",
                "Content-Type": "application/json"
            }
            
            # 请求数据
            data = {
                "message": f"Upload image {filename}",
                "content": content_base64,
                "branch": "main"
            }
            
            # 发送请求
            response = requests.put(api_url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 201:
                # 上传成功
                result = response.json()
                download_url = f"https://raw.githubusercontent.com/{username}/{repo}/main/{file_path}"
                
                self.logger.log(f"Image uploaded successfully: {download_url}")
                
                return {
                    "success": True,
                    "url": download_url,
                    "filename": filename,
                    "size": len(file_content),
                    "github_url": result.get("content", {}).get("html_url", "")
                }
            else:
                # 上传失败
                error_msg = f"GitHub API error: {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f" - {error_data.get('message', 'Unknown error')}"
                except:
                    error_msg += f" - {response.text}"
                
                self.logger.log(f"Upload failed: {error_msg}", "ERROR")
                
                return {
                    "success": False,
                    "error": error_msg
                }
                
        except Exception as e:
            error_msg = f"Upload error: {str(e)}"
            self.logger.log(error_msg, "ERROR")
            return {
                "success": False,
                "error": error_msg
            }
    
    def upload_image(self, file_content: bytes, original_filename: str) -> Dict[str, Any]:
        """上传图片到图床"""
        try:
            # 获取配置
            config = self.get_imagehost_config()
            
            # 检查配置
            if not config.get("github_token"):
                return {
                    "success": False,
                    "error": "GitHub Token未配置"
                }
            
            if not config.get("repo_path"):
                return {
                    "success": False,
                    "error": "仓库路径未配置"
                }
            
            # 检查文件大小
            max_size_mb = config.get("max_file_size", 3)
            if not self.validate_file_size(len(file_content), max_size_mb):
                return {
                    "success": False,
                    "error": f"文件大小超过限制 ({max_size_mb}MB)"
                }
            
            # 生成文件名
            filename = self.generate_filename(original_filename)
            
            # 上传到GitHub
            result = self.upload_to_github(file_content, filename, config)

            # 如果上传成功，保存到历史记录
            if result.get("success"):
                self._save_to_history(result, original_filename)

            return result

        except Exception as e:
            error_msg = f"Upload image error: {str(e)}"
            self.logger.log(error_msg, "ERROR")
            return {
                "success": False,
                "error": error_msg
            }
    
    def test_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试GitHub连接"""
        try:
            headers = {
                "Authorization": f"token {config.get('github_token')}",
                "Accept": "application/vnd.github.v3+json"
            }
            
            # 测试API连接
            response = requests.get("https://api.github.com/user", headers=headers, timeout=10)
            
            if response.status_code == 200:
                user_data = response.json()
                return {
                    "success": True,
                    "message": f"连接成功，用户: {user_data.get('login', 'Unknown')}"
                }
            else:
                return {
                    "success": False,
                    "error": f"API连接失败: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"连接测试失败: {str(e)}"
            }

    def _ensure_history_file(self):
        """确保历史记录文件存在"""
        if not os.path.exists(self.history_file):
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump([], f)

    def _save_to_history(self, upload_result: Dict[str, Any], original_filename: str):
        """保存上传记录到历史"""
        try:
            history = self.get_upload_history()

            # 创建历史记录项
            history_item = {
                "id": str(datetime.now().timestamp()).replace('.', ''),
                "original_filename": original_filename,
                "uploaded_filename": upload_result.get("filename", ""),
                "url": upload_result.get("url", ""),
                "github_url": upload_result.get("github_url", ""),
                "file_size": upload_result.get("size", 0),
                "upload_time": datetime.now().isoformat(),
                "upload_timestamp": datetime.now().timestamp()
            }

            # 添加到历史记录开头（最新的在前面）
            history.insert(0, history_item)

            # 限制历史记录数量（保留最近100条）
            if len(history) > 100:
                history = history[:100]

            # 保存到文件
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)

            self.logger.log(f"Upload history saved: {original_filename}")

        except Exception as e:
            self.logger.log(f"Error saving upload history: {str(e)}", "ERROR")

    def get_upload_history(self) -> List[Dict[str, Any]]:
        """获取上传历史记录"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            self.logger.log(f"Error loading upload history: {str(e)}", "ERROR")
            return []

    def delete_history_item(self, item_id: str) -> bool:
        """删除历史记录项"""
        try:
            history = self.get_upload_history()
            original_length = len(history)

            # 过滤掉指定ID的记录
            history = [item for item in history if item.get("id") != item_id]

            if len(history) < original_length:
                # 保存更新后的历史记录
                with open(self.history_file, 'w', encoding='utf-8') as f:
                    json.dump(history, f, ensure_ascii=False, indent=2)

                self.logger.log(f"History item deleted: {item_id}")
                return True
            else:
                self.logger.log(f"History item not found: {item_id}", "WARNING")
                return False

        except Exception as e:
            self.logger.log(f"Error deleting history item: {str(e)}", "ERROR")
            return False

    def clear_history(self) -> bool:
        """清空所有历史记录"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump([], f)

            self.logger.log("Upload history cleared")
            return True

        except Exception as e:
            self.logger.log(f"Error clearing history: {str(e)}", "ERROR")
            return False
