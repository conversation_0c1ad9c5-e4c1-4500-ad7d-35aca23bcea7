from typing import Dict, Any, List, Optional
from .config_manager import ConfigManager
from datetime import datetime
import uuid

class CharacterManager:
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
    
    def get_characters(self) -> List[Dict[str, Any]]:
        """获取角色配置，返回新格式的角色数据"""
        characters = self.config_manager.get_characters()

        # 检查是否需要添加默认字段
        needs_save = False

        # 确保每个角色都有必要的字段
        for character in characters:
            if "display_type" not in character:
                character["display_type"] = "name"
                needs_save = True
            if "outfits" not in character:
                character["outfits"] = []
                needs_save = True
            if "created_at" not in character:
                character["created_at"] = datetime.now().isoformat()
                needs_save = True
            if "updated_at" not in character:
                character["updated_at"] = datetime.now().isoformat()
                needs_save = True

        # 如果有修改，保存配置
        if needs_save:
            self.config_manager.save_characters(characters)

        return characters

    def get_character_by_code(self, character_code: str) -> Optional[Dict[str, Any]]:
        """根据角色编码获取角色"""
        if not character_code:
            return None

        # 直接从配置管理器获取数据，避免缓存问题
        characters = self.config_manager.get_characters()

        # 确保数据格式正确
        for character in characters:
            if "display_type" not in character:
                character["display_type"] = "name"
            if "outfits" not in character:
                character["outfits"] = []

        # 查找匹配的角色
        for char in characters:
            if char and char.get("character_code") == character_code:
                return char
        return None

    def get_outfit_by_id(self, character_code: str, outfit_id: str) -> Optional[Dict[str, Any]]:
        """根据角色编码和服装ID获取服装"""
        character = self.get_character_by_code(character_code)
        if not character:
            return None

        for outfit in character.get("outfits", []):
            if outfit.get("outfit_id") == outfit_id:
                return outfit
        return None
    
    def add_character(self, character_data: Dict[str, Any]) -> str:
        """添加新角色（新格式）"""
        characters = self.get_characters()

        # 验证必需字段
        required_fields = ["character_name", "character_code"]
        for field in required_fields:
            if field not in character_data or not character_data[field]:
                raise ValueError(f"Missing required field: {field}")

        # 检查角色编码唯一性
        character_code = character_data["character_code"]
        if self.get_character_by_code(character_code):
            raise ValueError(f"Character code '{character_code}' already exists")

        # 设置默认值
        now = datetime.now().isoformat()
        new_character = {
            "character_code": character_code,
            "character_name": character_data.get("character_name", ""),
            "core_features": character_data.get("core_features", ""),
            "display_type": character_data.get("display_type", "name"),
            "outfits": [],
            "created_at": now,
            "updated_at": now
        }

        characters.append(new_character)
        self.config_manager.save_characters(characters)
        return character_code

    def add_outfit(self, character_code: str, outfit_data: Dict[str, Any]) -> str:
        """为角色添加新服装"""
        characters = self.get_characters()
        character = self.get_character_by_code(character_code)

        if not character:
            raise ValueError(f"Character with code '{character_code}' not found")

        # 验证必需字段
        required_fields = ["outfit_name", "image_url"]
        for field in required_fields:
            if field not in outfit_data or not outfit_data[field]:
                raise ValueError(f"Missing required field: {field}")

        # 生成服装ID
        existing_outfit_ids = [outfit.get("outfit_id", "") for outfit in character.get("outfits", [])]
        outfit_id = f"outfit_{len(existing_outfit_ids) + 1}"

        # 确保ID唯一性
        while outfit_id in existing_outfit_ids:
            outfit_id = f"outfit_{uuid.uuid4().hex[:8]}"

        # 创建新服装
        new_outfit = {
            "outfit_id": outfit_id,
            "outfit_name": outfit_data.get("outfit_name", ""),
            "outfit_description": outfit_data.get("outfit_description", ""),
            "image_url": outfit_data.get("image_url", ""),
            "created_at": datetime.now().isoformat()
        }

        # 添加到角色的服装列表
        character["outfits"].append(new_outfit)
        character["updated_at"] = datetime.now().isoformat()

        self.config_manager.save_characters(characters)
        return outfit_id
    
    def update_character(self, character_code: str, character_data: Dict[str, Any]):
        """更新角色信息"""
        characters = self.get_characters()
        character = self.get_character_by_code(character_code)

        if not character:
            raise ValueError(f"Character with code '{character_code}' not found")

        # 更新角色信息
        if "character_name" in character_data:
            character["character_name"] = character_data["character_name"]
        if "core_features" in character_data:
            character["core_features"] = character_data["core_features"]
        if "display_type" in character_data:
            character["display_type"] = character_data["display_type"]

        character["updated_at"] = datetime.now().isoformat()

        self.config_manager.save_characters(characters)

    def update_outfit(self, character_code: str, outfit_id: str, outfit_data: Dict[str, Any]):
        """更新服装信息"""
        characters = self.get_characters()
        character = self.get_character_by_code(character_code)

        if not character:
            raise ValueError(f"Character with code '{character_code}' not found")

        outfit = self.get_outfit_by_id(character_code, outfit_id)
        if not outfit:
            raise ValueError(f"Outfit with id '{outfit_id}' not found")

        # 更新服装信息
        if "outfit_name" in outfit_data:
            outfit["outfit_name"] = outfit_data["outfit_name"]
        if "outfit_description" in outfit_data:
            outfit["outfit_description"] = outfit_data["outfit_description"]
        if "image_url" in outfit_data:
            outfit["image_url"] = outfit_data["image_url"]

        character["updated_at"] = datetime.now().isoformat()

        self.config_manager.save_characters(characters)
    
    def delete_character(self, character_code: str):
        """删除角色及其所有服装"""
        characters = self.get_characters()
        original_count = len(characters)

        characters = [char for char in characters if char.get("character_code") != character_code]

        if len(characters) == original_count:
            raise ValueError(f"Character with code '{character_code}' not found")

        self.config_manager.save_characters(characters)

    def delete_outfit(self, character_code: str, outfit_id: str):
        """删除指定服装"""
        characters = self.get_characters()
        character = self.get_character_by_code(character_code)

        if not character:
            raise ValueError(f"Character with code '{character_code}' not found")

        outfits = character.get("outfits", [])
        for i, outfit in enumerate(outfits):
            if outfit.get("outfit_id") == outfit_id:
                del outfits[i]
                character["updated_at"] = datetime.now().isoformat()
                self.config_manager.save_characters(characters)
                return

        raise ValueError(f"Outfit with id '{outfit_id}' not found")
    
    def get_character_by_id(self, character_id: str) -> Optional[Dict[str, Any]]:
        characters = self.get_characters()
        for char in characters:
            if char.get("id") == character_id:
                return char
        return None
    
    def get_character_by_code(self, code: str) -> Optional[Dict[str, Any]]:
        characters = self.get_characters()
        for char in characters:
            if char.get("code") == code:
                return char
        return None
    
    def validate_character_data(self, character_data: Dict[str, Any]) -> List[str]:
        errors = []
        
        required_fields = ["name", "code"]
        for field in required_fields:
            if field not in character_data or not character_data[field]:
                errors.append(f"Missing required field: {field}")
        
        if "code" in character_data:
            code = character_data["code"]
            if not isinstance(code, str) or len(code.strip()) == 0:
                errors.append("Character code must be a non-empty string")
            else:
                existing_char = self.get_character_by_code(code)
                if existing_char and existing_char.get("id") != character_data.get("id"):
                    errors.append(f"Character code '{code}' already exists")
        
        if "url" in character_data:
            url = character_data["url"]
            if url and not (url.startswith("http://") or url.startswith("https://")):
                errors.append("Character URL must be a valid HTTP/HTTPS URL")
        
        return errors
    
    def save_character(self, character_data: Dict[str, Any]) -> str:
        validation_errors = self.validate_character_data(character_data)
        if validation_errors:
            raise ValueError("; ".join(validation_errors))
        
        if "id" in character_data and character_data["id"]:
            self.update_character(character_data["id"], character_data)
            return character_data["id"]
        else:
            return self.add_character(character_data)
    
    def generate_reference_text(self, selected_character_ids: List[str], use_core_features: bool = True) -> str:
        """生成参考文本，支持新旧格式

        Args:
            selected_character_ids: 选中的角色ID列表
            use_core_features: 是否包含角色核心特征，默认为True
        """
        if not selected_character_ids:
            return ""

        # 直接获取角色数据，避免方法调用问题
        characters = self.config_manager.get_characters()

        reference_texts = []
        for i, selection_id in enumerate(selected_character_ids):
            character = None

            # 检查是否为新格式（包含冒号）
            if ':' in selection_id:
                # 新格式：characterCode:outfitId
                character_code, outfit_id = selection_id.split(':', 1)
                # 直接查找角色
                for char in characters:
                    if char.get('character_code') == character_code:
                        character = char
                        break
            else:
                # 旧格式：直接是角色ID
                for char in characters:
                    if char.get('id') == selection_id:
                        character = char
                        break

            if character:
                # 根据角色的display_type设置选择使用名称还是编码
                display_type = character.get("display_type", "name")
                if display_type == "code":
                    char_display_name = character.get("character_code") or character.get("code", f"角色{i+1}")
                else:
                    char_display_name = character.get("character_name") or character.get("name", f"角色{i+1}")

                position_text = f"第{self._number_to_chinese(i+1)}张图片"
                reference_text = f"{char_display_name}的角色形象:请严格参考我提供的{position_text}(角色参考图)来塑造角色。"

                # 根据开关决定是否添加角色核心特征
                if use_core_features:
                    core_features = character.get("core_features", "")
                    if core_features and core_features.strip():
                        reference_text += f"角色不可变的核心特征=【{core_features.strip()}】"

                reference_texts.append(reference_text)

        return "".join(reference_texts)
    
    def _number_to_chinese(self, num: int) -> str:
        chinese_nums = ["一", "二", "三", "四", "五"]
        if 1 <= num <= 5:
            return chinese_nums[num - 1]
        return str(num)
    
    def get_character_urls(self, selected_character_ids: List[str]) -> List[str]:
        """获取角色图片URL列表，支持新旧格式"""
        # 直接获取角色数据，避免方法调用问题
        characters = self.config_manager.get_characters()

        urls = []
        for selection_id in selected_character_ids:
            image_url = None

            # 检查是否为新格式（包含冒号）
            if ':' in selection_id:
                # 新格式：characterCode:outfitId
                character_code, outfit_id = selection_id.split(':', 1)
                # 直接查找角色
                character = None
                for char in characters:
                    if char.get('character_code') == character_code:
                        character = char
                        break

                if character:
                    # 查找指定的服装
                    for outfit in character.get('outfits', []):
                        if outfit.get('outfit_id') == outfit_id:
                            image_url = outfit.get('image_url')
                            break
            else:
                # 旧格式：直接是角色ID
                for char in characters:
                    if char.get('id') == selection_id:
                        image_url = char.get('url')
                        break

            if image_url:
                urls.append(image_url)

        return urls
    
    def validate_selected_characters(self, selected_character_ids: List[str]) -> List[str]:
        errors = []
        
        if len(selected_character_ids) > 5:
            errors.append("最多只能选择5个角色")
        
        for char_id in selected_character_ids:
            character = self.get_character_by_id(char_id)
            if not character:
                errors.append(f"角色ID {char_id} 不存在")
            elif not character.get("url"):
                errors.append(f"角色 '{character.get('name', char_id)}' 的URL为空")
        
        return errors

    # ===== 兼容性方法 =====

    def get_characters_for_selection(self) -> List[Dict[str, Any]]:
        """获取用于选择的角色列表（展开为角色-服装组合）"""
        characters = self.get_characters()
        selection_items = []

        for character in characters:
            character_code = character.get("character_code", "")
            character_name = character.get("character_name", "")
            core_features = character.get("core_features", "")
            display_type = character.get("display_type", "name")

            outfits = character.get("outfits", [])
            if not outfits:
                # 如果没有服装，创建一个默认项
                selection_items.append({
                    "id": f"{character_code}:default",
                    "character_code": character_code,
                    "outfit_id": "default",
                    "name": character_name,
                    "code": character_code,
                    "core_features": core_features,
                    "display_type": display_type,
                    "url": "",
                    "outfit_name": "默认"
                })
            else:
                # 为每个服装创建一个选择项
                for outfit in outfits:
                    selection_items.append({
                        "id": f"{character_code}:{outfit.get('outfit_id', '')}",
                        "character_code": character_code,
                        "outfit_id": outfit.get("outfit_id", ""),
                        "name": character_name,
                        "code": character_code,
                        "core_features": core_features,
                        "display_type": display_type,
                        "url": outfit.get("image_url", ""),
                        "outfit_name": outfit.get("outfit_name", "")
                    })

        return selection_items

    def generate_reference_text_new(self, selected_items: List[Dict[str, str]], use_core_features: bool = True) -> str:
        """生成参考文本（新格式：角色编码+服装ID）

        Args:
            selected_items: 选中的角色和服装项目列表
            use_core_features: 是否包含角色核心特征，默认为True
        """
        if not selected_items:
            return ""

        reference_texts = []
        for i, item in enumerate(selected_items):
            character_code = item.get("character_code", "")
            outfit_id = item.get("outfit_id", "")

            character = self.get_character_by_code(character_code)
            if not character:
                continue

            # 根据角色的display_type设置选择使用名称还是编码
            display_type = character.get("display_type", "name")
            if display_type == "code":
                char_display_name = character.get("character_code", f"角色{i+1}")
            else:
                char_display_name = character.get("character_name", f"角色{i+1}")

            position_text = f"第{self._number_to_chinese(i+1)}张图片"
            reference_text = f"{char_display_name}的角色形象:请严格参考我提供的{position_text}(角色参考图)来塑造角色。"

            # 根据开关决定是否添加角色核心特征
            if use_core_features:
                core_features = character.get("core_features", "")
                if core_features and core_features.strip():
                    reference_text += f"角色不可变的核心特征=【{core_features.strip()}】"

            reference_texts.append(reference_text)

        return "".join(reference_texts)

    def _number_to_chinese(self, num: int) -> str:
        chinese_numbers = ["", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"]
        if 1 <= num <= 10:
            return chinese_numbers[num]
        return str(num)