import requests
import time
from typing import Dict, Any, List, Optional
from .config_manager import ConfigManager

class ModelService:
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
    
    def get_service_by_id(self, service_id: str) -> Optional[Dict[str, Any]]:
        services = self.config_manager.get_model_config()["services"]
        for service in services:
            if str(service.get("id")) == str(service_id):
                return service
        return None
    
    def get_default_service(self) -> Optional[Dict[str, Any]]:
        config = self.config_manager.get_model_config()
        default_service_id = config.get("default_service")
        if default_service_id:
            return self.get_service_by_id(default_service_id)
        services = config.get("services", [])
        return services[0] if services else None
    
    def get_models_for_service(self, service_id: str, model_type: str = "image") -> List[str]:
        service = self.get_service_by_id(service_id)
        if not service:
            return []
        
        models = service.get("models", [])
        filtered_models = [m for m in models if m.get("type") == model_type]
        return [m.get("name", "") for m in filtered_models]
    
    def call_api(self, service_id: str, model: str, payload: Dict[str, Any], 
                 trace_id: str = None) -> Dict[str, Any]:
        service = self.get_service_by_id(service_id)
        if not service:
            raise Exception(f"Service {service_id} not found")
        
        api_url = service.get("api_url", "")
        api_key = service.get("api_key", "")
        timeout = service.get("timeout", 180)
        retry_count = service.get("retry_count", 3)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        payload["model"] = model
        
        # 记录API调用开始
        from .logger import Logger
        logger = Logger()
        logger.log(f"开始API调用: {api_url}", "INFO", trace_id)
        
        for attempt in range(retry_count):
            try:
                import time
                import uuid
                start_time = time.time()

                # 为每次API调用生成唯一的调用ID
                api_call_id = str(uuid.uuid4())[:8]
                attempt_suffix = f"_attempt_{attempt + 1}" if attempt > 0 else ""
                full_api_call_id = f"{api_call_id}{attempt_suffix}"

                # 记录API调用开始
                logger.log_api_call_start(
                    url=api_url,
                    headers=headers,
                    payload=payload,
                    trace_id=trace_id,
                    api_call_id=full_api_call_id
                )

                response = requests.post(
                    api_url,
                    json=payload,
                    headers=headers,
                    timeout=timeout
                )

                duration = time.time() - start_time
                logger.log(f"API响应状态: {response.status_code}, 耗时: {duration:.2f}秒", "INFO", trace_id)

                if response.status_code == 200:
                    result = response.json()

                    # 记录API调用结束
                    logger.log_api_call_end(
                        url=api_url,
                        response_status=response.status_code,
                        response_data=result,
                        trace_id=trace_id,
                        duration=duration,
                        api_call_id=full_api_call_id
                    )

                    return result
                else:
                    error_msg = f"API调用失败，状态码: {response.status_code}, 响应: {response.text}"

                    # 记录API调用结束（错误响应）
                    logger.log_api_call_end(
                        url=api_url,
                        response_status=response.status_code,
                        response_data=response.text,
                        trace_id=trace_id,
                        duration=duration,
                        api_call_id=full_api_call_id
                    )

                    logger.log(error_msg, "ERROR", trace_id)
                    if attempt == retry_count - 1:
                        raise Exception(error_msg)
                    time.sleep(2 ** attempt)

            except requests.exceptions.Timeout:
                duration = time.time() - start_time
                error_msg = f"API调用超时，超过 {timeout} 秒"

                # 记录API调用结束（超时）
                logger.log_api_call_end(
                    url=api_url,
                    response_status=None,
                    response_data=f"请求超时 ({timeout}秒)",
                    trace_id=trace_id,
                    duration=duration,
                    api_call_id=full_api_call_id
                )

                logger.log(error_msg, "WARNING", trace_id)
                if attempt == retry_count - 1:
                    raise Exception(error_msg)
                time.sleep(2 ** attempt)

            except Exception as e:
                duration = time.time() - start_time if 'start_time' in locals() else 0
                error_msg = f"API调用异常: {str(e)}"

                # 记录API调用结束（异常）
                logger.log_api_call_end(
                    url=api_url,
                    response_status=None,
                    response_data=f"异常: {str(e)}",
                    trace_id=trace_id,
                    duration=duration,
                    api_call_id=full_api_call_id
                )

                logger.log(error_msg, "ERROR", trace_id)
                if attempt == retry_count - 1:
                    raise e
                time.sleep(2 ** attempt)
        
        raise Exception("API调用失败，已达到最大重试次数")
    
    def validate_service_config(self, service_config: Dict[str, Any]) -> List[str]:
        errors = []
        
        required_fields = ["name", "api_url", "api_key"]
        for field in required_fields:
            if not service_config.get(field):
                errors.append(f"Missing required field: {field}")
        
        if "models" in service_config:
            models = service_config["models"]
            if not isinstance(models, list):
                errors.append("Models must be a list")
            else:
                for i, model in enumerate(models):
                    if not isinstance(model, dict):
                        errors.append(f"Model {i} must be an object")
                    elif not model.get("name"):
                        errors.append(f"Model {i} missing name")
                    elif not model.get("type"):
                        errors.append(f"Model {i} missing type")
        
        return errors