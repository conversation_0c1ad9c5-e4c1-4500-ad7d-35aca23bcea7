import re
import os
import hashlib
import requests
import threading
from urllib.parse import urlparse
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from .model_service import ModelService
from .character_manager import CharacterManager
from .logger import Logger

class ImageGenerator:
    def __init__(self, model_service: ModelService, config_manager=None):
        self.model_service = model_service
        self.config_manager = config_manager
        self.logger = Logger()
        self.progress_data = {}
        self.progress_lock = threading.Lock()
    
    def get_progress(self, trace_id: str) -> Dict[str, Any]:
        with self.progress_lock:
            return self.progress_data.get(trace_id, {"status": "not_found"})
    
    def update_progress(self, trace_id: str, current: int, total: int, message: str):
        with self.progress_lock:
            self.progress_data[trace_id] = {
                "current": current,
                "total": total,
                "percentage": (current / total * 100) if total > 0 else 0,
                "message": message,
                "status": "in_progress"
            }
    
    def complete_progress(self, trace_id: str, success: bool, message: str):
        with self.progress_lock:
            if trace_id in self.progress_data:
                self.progress_data[trace_id].update({
                    "status": "completed" if success else "failed",
                    "message": message,
                    "percentage": 100 if success else self.progress_data[trace_id].get("percentage", 0)
                })
    
    def generate_single_scene(self, scene_data: Dict[str, Any], trace_id: str) -> Dict[str, Any]:
        try:
            self.logger.log(f"开始单个分镜生成: {scene_data.get('scene_number')}", "INFO", trace_id)
            
            service_id = scene_data.get("service_id")
            model = scene_data.get("model")
            prompt = scene_data.get("prompt", "")
            selected_characters = scene_data.get("selected_characters", [])
            image_count = scene_data.get("image_count", 1)
            save_directory = scene_data.get("save_directory", "")
            excel_filename = scene_data.get("excel_filename", "")
            use_core_features = scene_data.get("use_core_features", True)  # 默认启用核心特征
            
            # 验证必需参数
            if not service_id:
                raise Exception("缺少模型服务ID")
            if not model:
                raise Exception("缺少模型名称")
            if not prompt:
                raise Exception("缺少生图提示")
            
            self.logger.log(f"生成参数: service_id={service_id}, model={model}, prompt='{prompt[:50]}...', image_count={image_count}", "INFO", trace_id)
            
            generated_images = []
            
            # 并发生成多张图片
            def generate_single_image(batch_index):
                batch_trace_id = f"{trace_id}_batch_{batch_index+1}"
                self.logger.log(f"并发生成第 {batch_index+1}/{image_count} 张图片", "INFO", batch_trace_id)
                
                try:
                    payload = self._build_api_payload(prompt, selected_characters, model, use_core_features)
                    self.logger.log(f"API请求载荷构建完成", "INFO", batch_trace_id)
                    
                    response = self.model_service.call_api(service_id, model, payload, batch_trace_id)
                    self.logger.log(f"API调用成功，开始提取图片URL", "INFO", batch_trace_id)
                    
                    image_urls = self._extract_image_urls(response)
                    self.logger.log(f"提取到 {len(image_urls)} 个图片URL", "INFO", batch_trace_id)
                    
                    if not image_urls:
                        self.logger.log("警告: 未从API响应中提取到图片URL", "WARNING", batch_trace_id)
                        return []
                    
                    unique_urls_with_content = self._deduplicate_images_with_content(image_urls, batch_trace_id)
                    self.logger.log(f"去重后剩余 {len(unique_urls_with_content)} 个图片", "INFO", batch_trace_id)

                    batch_images = []
                    for j, (url, content) in enumerate(unique_urls_with_content):
                        try:
                            saved_path = self._save_image_with_content(
                                url,
                                content,
                                excel_filename,
                                scene_data.get("scene_number", ""),
                                batch_index * len(unique_urls_with_content) + j + 1,
                                save_directory
                            )
                            batch_images.append({
                                "url": url,
                                "local_path": saved_path,
                                "batch": batch_index + 1,
                                "index": j + 1
                            })
                            self.logger.log(f"图片保存成功: {saved_path}", "INFO", batch_trace_id)
                        except Exception as e:
                            self.logger.log(f"保存图片失败 {url}: {str(e)}", "ERROR", batch_trace_id)
                    
                    return batch_images
                    
                except Exception as e:
                    self.logger.log(f"第 {batch_index+1} 次生图失败: {str(e)}", "ERROR", batch_trace_id)
                    return []
            
            # 使用线程池并发生成图片
            with ThreadPoolExecutor(max_workers=image_count) as executor:
                self.logger.log(f"开始并发生成 {image_count} 张图片，最大并发数: {image_count}", "INFO", trace_id)
                
                # 提交所有任务
                future_to_batch = {executor.submit(generate_single_image, i): i for i in range(image_count)}
                
                # 收集结果
                for future in as_completed(future_to_batch):
                    batch_index = future_to_batch[future]
                    try:
                        batch_images = future.result()
                        generated_images.extend(batch_images)
                        self.logger.log(f"批次 {batch_index+1} 完成，生成 {len(batch_images)} 张图片", "INFO", trace_id)
                    except Exception as e:
                        self.logger.log(f"批次 {batch_index+1} 异常: {str(e)}", "ERROR", trace_id)
            
            success_message = f"分镜生成完成，成功生成 {len(generated_images)} 张图片"
            self.logger.log(success_message, "INFO", trace_id)
            
            return {
                "success": True,
                "generated_images": generated_images,
                "total_count": len(generated_images)
            }
            
        except Exception as e:
            error_message = f"单个分镜生成失败: {str(e)}"
            self.logger.log(error_message, "ERROR", trace_id)
            raise Exception(error_message)
    
    def generate_batch_scenes(self, scenes_data: List[Dict[str, Any]], trace_id: str) -> Dict[str, Any]:
        try:
            enabled_scenes = [scene for scene in scenes_data if scene.get("enabled", True)]
            self.logger.log(f"Starting batch generation for {len(enabled_scenes)} scenes (total: {len(scenes_data)})", "INFO", trace_id)
            self.update_progress(trace_id, 0, len(enabled_scenes), "开始批量生成...")
            
            results = []
            total_images = 0
            failed_scenes = []
            completed_scenes = 0
            
            # 并发处理分镜
            def process_single_scene(scene_data, scene_index):
                scene_trace_id = f"{trace_id}_scene_{scene_index+1}"
                scene_number = scene_data.get('scene_number')
                
                try:
                    self.logger.log(f"开始并发处理分镜 {scene_number}", "INFO", scene_trace_id)
                    result = self.generate_single_scene(scene_data, scene_trace_id)
                    
                    return {
                        "scene_number": scene_number,
                        "success": True,
                        "images": result["generated_images"],
                        "total_count": result["total_count"]
                    }
                    
                except Exception as e:
                    self.logger.log(f"分镜 {scene_number} 处理失败: {str(e)}", "ERROR", scene_trace_id)
                    return {
                        "scene_number": scene_number,
                        "success": False,
                        "error": str(e)
                    }
            
            # 使用线程池并发处理所有分镜
            max_concurrent_scenes = len(enabled_scenes)  # 所有分镜同时处理，实现真正的并发
            with ThreadPoolExecutor(max_workers=max_concurrent_scenes) as executor:
                self.logger.log(f"开始并发处理 {len(enabled_scenes)} 个分镜，最大并发数: {max_concurrent_scenes}", "INFO", trace_id)
                
                # 提交所有分镜任务
                future_to_scene = {
                    executor.submit(process_single_scene, scene_data, i): (scene_data, i) 
                    for i, scene_data in enumerate(enabled_scenes)
                }
                
                # 收集结果
                for future in as_completed(future_to_scene):
                    scene_data, scene_index = future_to_scene[future]
                    completed_scenes += 1
                    
                    try:
                        result = future.result()
                        
                        if result["success"]:
                            results.append({
                                "scene_number": result["scene_number"],
                                "success": True,
                                "images": result["images"]
                            })
                            total_images += result["total_count"]
                            self.logger.log(f"分镜 {result['scene_number']} 完成，生成 {result['total_count']} 张图片", "INFO", trace_id)
                        else:
                            failed_scenes.append({
                                "scene_number": result["scene_number"],
                                "error": result["error"]
                            })
                            self.logger.log(f"分镜 {result['scene_number']} 失败: {result['error']}", "ERROR", trace_id)
                        
                        # 更新进度
                        self.update_progress(trace_id, completed_scenes, len(enabled_scenes), 
                                           f"已完成 {completed_scenes}/{len(enabled_scenes)} 个分镜")
                        
                    except Exception as e:
                        scene_number = scene_data.get('scene_number')
                        error_msg = str(e)
                        self.logger.log(f"分镜 {scene_number} 异常: {error_msg}", "ERROR", trace_id)
                        failed_scenes.append({
                            "scene_number": scene_number,
                            "error": error_msg
                        })
            
            success_message = f"批量生成完成。总图片: {total_images}, 成功分镜: {len(results)}, 失败分镜: {len(failed_scenes)}"
            self.logger.log(success_message, "INFO", trace_id)
            self.complete_progress(trace_id, True, success_message)
            
            return {
                "success": True,
                "total_images": total_images,
                "successful_scenes": len(results),
                "failed_scenes": failed_scenes,
                "results": results
            }
            
        except Exception as e:
            error_message = f"Error in batch generation: {str(e)}"
            self.logger.log(error_message, "ERROR", trace_id)
            self.complete_progress(trace_id, False, error_message)
            raise e
    
    def _build_api_payload(self, prompt: str, selected_characters: List[str], model: str, use_core_features: bool = True) -> Dict[str, Any]:
        if selected_characters:
            character_manager = CharacterManager(self.model_service.config_manager)
            reference_text = character_manager.generate_reference_text(selected_characters, use_core_features)
            character_urls = character_manager.get_character_urls(selected_characters)
            
            content = [
                {"type": "text", "text": reference_text + prompt}
            ]
            
            for url in character_urls:
                content.append({
                    "type": "image_url",
                    "image_url": {"url": url}
                })
            
            payload = {
                "model": model,
                "messages": [{
                    "role": "user",
                    "content": content
                }],
                "stream": False
            }
        else:
            payload = {
                "model": model,
                "messages": [{
                    "role": "user",
                    "content": [{"type": "text", "text": prompt}]
                }],
                "stream": False
            }
        
        return payload
    
    def _extract_image_urls(self, response: Dict[str, Any]) -> List[str]:
        urls = []
        
        try:
            if "choices" in response and len(response["choices"]) > 0:
                message = response["choices"][0].get("message", {})
                content = message.get("content", "")
                
                url_patterns = [
                    r'https://videos\.openai\.com/[^\s\)]+\.png[^\s\)]*',
                    r'https://filesystem\.site/cdn/[^\s\)]+\.png',
                    r'https://[^\s\)]+\.(?:png|jpg|jpeg|gif|webp)'
                ]
                
                for pattern in url_patterns:
                    found_urls = re.findall(pattern, content)
                    urls.extend(found_urls)
                
        except Exception as e:
            self.logger.log(f"Error extracting image URLs: {str(e)}", "ERROR")
        
        return urls
    
    def _deduplicate_images(self, image_urls: List[str]) -> List[str]:
        """
        对图片URL进行去重，包括：
        1. URL去重
        2. 基于图片内容MD5的去重
        """
        # 第一步：URL去重
        unique_urls = list(dict.fromkeys(image_urls))  # 保持顺序的去重
        self.logger.log(f"URL去重: {len(image_urls)} -> {len(unique_urls)}", "INFO")

        # 第二步：基于图片内容MD5的去重
        final_urls = []
        seen_md5_hashes = set()

        for url in unique_urls:
            try:
                # 下载图片内容计算MD5
                response = requests.get(url, timeout=30)
                if response.status_code == 200:
                    image_content = response.content
                    image_md5 = hashlib.md5(image_content).hexdigest()

                    if image_md5 not in seen_md5_hashes:
                        seen_md5_hashes.add(image_md5)
                        final_urls.append(url)
                        self.logger.log(f"图片MD5: {image_md5[:8]}... - 保留", "INFO")
                    else:
                        self.logger.log(f"图片MD5: {image_md5[:8]}... - 重复，跳过", "INFO")
                else:
                    self.logger.log(f"无法访问图片URL: {url}, 状态码: {response.status_code}", "WARNING")

            except Exception as e:
                self.logger.log(f"处理图片URL时出错 {url}: {str(e)}", "ERROR")
                # 如果出错，仍然保留这个URL，避免丢失
                if url not in final_urls:
                    final_urls.append(url)

        self.logger.log(f"内容去重: {len(unique_urls)} -> {len(final_urls)}", "INFO")
        return final_urls

    def _deduplicate_images_with_content(self, image_urls: List[str], trace_id: str) -> List[tuple]:
        """
        对图片URL进行去重，返回(url, content)元组列表
        1. URL去重
        2. 基于图片内容MD5的去重
        """
        # 第一步：URL去重
        unique_urls = list(dict.fromkeys(image_urls))  # 保持顺序的去重
        self.logger.log(f"URL去重: {len(image_urls)} -> {len(unique_urls)}", "INFO", trace_id)

        # 第二步：基于图片内容MD5的去重
        final_results = []
        seen_md5_hashes = set()

        for url in unique_urls:
            try:
                # 下载图片内容计算MD5
                response = requests.get(url, timeout=30)
                if response.status_code == 200:
                    image_content = response.content
                    image_md5 = hashlib.md5(image_content).hexdigest()

                    if image_md5 not in seen_md5_hashes:
                        seen_md5_hashes.add(image_md5)
                        final_results.append((url, image_content))
                        self.logger.log(f"图片MD5: {image_md5[:8]}... - 保留", "INFO", trace_id)
                    else:
                        self.logger.log(f"图片MD5: {image_md5[:8]}... - 重复，跳过", "INFO", trace_id)
                else:
                    self.logger.log(f"无法访问图片URL: {url}, 状态码: {response.status_code}", "WARNING", trace_id)

            except Exception as e:
                self.logger.log(f"处理图片URL时出错 {url}: {str(e)}", "ERROR", trace_id)

        self.logger.log(f"内容去重: {len(unique_urls)} -> {len(final_results)}", "INFO", trace_id)
        return final_results

    def _save_image_with_content(self, url: str, content: bytes, excel_filename: str,
                                scene_number: str, image_index: int, save_directory: str) -> str:
        """使用已下载的图片内容保存图片，避免重复下载和文件覆盖"""
        try:
            base_name = os.path.splitext(excel_filename)[0] if excel_filename else "image"

            # 获取配置设置
            settings = self._get_save_settings()

            # 根据配置生成文件名
            base_filename = self._generate_filename(base_name, scene_number, image_index, settings)

            # 确定保存目录
            if save_directory and os.path.exists(save_directory):
                target_directory = save_directory
            else:
                target_directory = os.path.join(os.getcwd(), "generated_images")
                os.makedirs(target_directory, exist_ok=True)

            # 根据配置决定是否防止覆盖
            if settings.get("prevent_overwrite", True):
                filepath = self._generate_unique_filepath(target_directory, base_filename)
            else:
                filepath = os.path.join(target_directory, base_filename)

            # 保存文件
            with open(filepath, 'wb') as f:
                f.write(content)

            return filepath

        except Exception as e:
            raise Exception(f"Failed to save image from {url}: {str(e)}")

    def _get_save_settings(self) -> Dict[str, Any]:
        """获取保存设置"""
        if self.config_manager:
            try:
                config = self.config_manager.get_model_config()
                return config.get("settings", {})
            except:
                pass

        # 默认设置
        return {
            "prevent_overwrite": True,
            "add_timestamp": True,
            "filename_format": "timestamp"
        }

    def _generate_filename(self, base_name: str, scene_number: str, image_index: int, settings: Dict[str, Any]) -> str:
        """根据配置生成文件名"""
        filename_format = settings.get("filename_format", "timestamp")
        add_timestamp = settings.get("add_timestamp", True)

        if filename_format == "timestamp" and add_timestamp:
            # 格式：{excel_name}_分镜{scene_number}_{millisecond_timestamp}.png
            import time
            millisecond_timestamp = int(time.time() * 1000)
            return f"{base_name}_分镜{scene_number}_{millisecond_timestamp}.png"
        elif filename_format == "simple":
            # 格式：{excel_name}_分镜{scene_number}_{image_index}.png
            return f"{base_name}_分镜{scene_number}_{image_index}.png"
        else:
            # 默认格式，包含毫秒时间戳
            import time
            millisecond_timestamp = int(time.time() * 1000)
            return f"{base_name}_分镜{scene_number}_{millisecond_timestamp}.png"

    def _generate_unique_filepath(self, directory: str, filename: str) -> str:
        """生成唯一的文件路径，如果文件已存在则添加序号"""
        filepath = os.path.join(directory, filename)

        # 如果文件不存在，直接返回
        if not os.path.exists(filepath):
            return filepath

        # 如果文件存在，添加序号
        name, ext = os.path.splitext(filename)
        counter = 1

        while True:
            new_filename = f"{name}_({counter}){ext}"
            new_filepath = os.path.join(directory, new_filename)

            if not os.path.exists(new_filepath):
                return new_filepath

            counter += 1

            # 防止无限循环，最多尝试1000次
            if counter > 1000:
                import uuid
                unique_id = str(uuid.uuid4())[:8]
                new_filename = f"{name}_{unique_id}{ext}"
                return os.path.join(directory, new_filename)

    def _save_image(self, url: str, excel_filename: str, scene_number: str,
                   image_index: int, save_directory: str) -> str:
        """下载并保存图片，防止文件覆盖"""
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()

            # 使用统一的保存逻辑
            return self._save_image_with_content(url, response.content, excel_filename,
                                               scene_number, image_index, save_directory)

        except Exception as e:
            raise Exception(f"Failed to save image from {url}: {str(e)}")
    
    def get_image_info(self, image_path: str) -> Dict[str, Any]:
        try:
            if os.path.exists(image_path):
                stat = os.stat(image_path)
                return {
                    "exists": True,
                    "size": stat.st_size,
                    "created": stat.st_ctime,
                    "modified": stat.st_mtime
                }
            else:
                return {"exists": False}
        except Exception:
            return {"exists": False}