#!/usr/bin/env python3
"""
TXT转Excel转换器模块
"""

import os
import csv
import pandas as pd
from datetime import datetime
from typing import Dict, Any, Optional
from .logger import Logger

class TxtToExcelConverter:
    def __init__(self):
        self.logger = Logger()
        
    def convert_txt_to_excel(self, file_content: str, original_filename: str, save_directory: str) -> Dict[str, Any]:
        """
        将TXT文件内容转换为Excel文件
        
        Args:
            file_content: TXT文件内容
            original_filename: 原始文件名
            save_directory: 保存目录
            
        Returns:
            转换结果字典
        """
        try:
            # 解析CSV格式的内容
            data = self._parse_csv_content(file_content)
            
            if not data:
                raise Exception("无法解析文件内容，请确保文件格式正确")
            
            # 验证数据格式
            self._validate_data_format(data)
            
            # 生成Excel文件
            excel_path = self._generate_excel_file(data, original_filename, save_directory)
            
            return {
                "success": True,
                "file_path": excel_path,
                "row_count": len(data),
                "message": "转换成功"
            }
            
        except Exception as e:
            self.logger.log(f"TXT转Excel转换失败: {str(e)}", "ERROR")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _parse_csv_content(self, content: str) -> list:
        """解析CSV格式的内容"""
        try:
            # 将内容按行分割
            lines = content.strip().split('\n')
            
            if len(lines) < 2:
                raise Exception("文件内容太少")
            
            # 使用csv模块解析
            import io
            csv_reader = csv.reader(io.StringIO(content))
            
            data = []
            headers = None
            
            for i, row in enumerate(csv_reader):
                if i == 0:
                    headers = [col.strip() for col in row]
                    # 验证必要的列
                    if not self._has_required_columns(headers):
                        raise Exception("缺少必要的列：分镜序号、文生图prompt")
                else:
                    if len(row) >= len(headers):
                        row_data = {}
                        for j, header in enumerate(headers):
                            if j < len(row):
                                row_data[header] = row[j].strip()
                        data.append(row_data)
            
            return data
            
        except Exception as e:
            raise Exception(f"解析CSV内容失败: {str(e)}")
    
    def _has_required_columns(self, headers: list) -> bool:
        """检查是否包含必要的列"""
        required_columns = ['分镜序号', '文生图prompt']
        
        for required in required_columns:
            if not any(required in header for header in headers):
                return False
        
        return True
    
    def _validate_data_format(self, data: list) -> None:
        """验证数据格式"""
        if not data:
            raise Exception("没有有效的数据行")
        
        # 检查每行数据
        for i, row in enumerate(data):
            if not isinstance(row, dict):
                raise Exception(f"第{i+2}行数据格式错误")
            
            # 检查必要字段
            scene_number = None
            prompt = None
            
            for key, value in row.items():
                if '分镜序号' in key:
                    scene_number = value
                elif '文生图prompt' in key:
                    prompt = value
            
            if not scene_number or not scene_number.strip():
                raise Exception(f"第{i+2}行缺少分镜序号")
            
            if not prompt or not prompt.strip():
                raise Exception(f"第{i+2}行缺少文生图prompt")
    
    def _generate_excel_file(self, data: list, original_filename: str, save_directory: str) -> str:
        """生成Excel文件"""
        try:
            # 确保保存目录存在
            os.makedirs(save_directory, exist_ok=True)
            
            # 生成文件名
            base_name = os.path.splitext(original_filename)[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_filename = f"{base_name}_converted_{timestamp}.xlsx"
            excel_path = os.path.join(save_directory, excel_filename)
            
            # 标准化数据格式
            standardized_data = []
            for row in data:
                standardized_row = {}
                
                # 查找分镜序号
                for key, value in row.items():
                    if '分镜序号' in key:
                        standardized_row['分镜序号'] = value
                        break
                
                # 查找文生图prompt
                for key, value in row.items():
                    if '文生图prompt' in key:
                        standardized_row['文生图prompt'] = value
                        break
                
                standardized_data.append(standardized_row)
            
            # 创建DataFrame
            df = pd.DataFrame(standardized_data)
            
            # 确保列的顺序
            df = df[['分镜序号', '文生图prompt']]
            
            # 保存为Excel文件
            df.to_excel(excel_path, index=False, engine='openpyxl')
            
            self.logger.log(f"Excel文件已生成: {excel_path}")
            return excel_path
            
        except Exception as e:
            raise Exception(f"生成Excel文件失败: {str(e)}")
    
    def validate_txt_content(self, content: str) -> Dict[str, Any]:
        """验证TXT内容是否可以转换为Excel"""
        try:
            # 基本格式检查
            lines = content.strip().split('\n')
            
            if len(lines) < 2:
                return {
                    "valid": False,
                    "error": "文件内容太少，至少需要标题行和一行数据"
                }
            
            # 检查是否包含引号（CSV格式特征）
            has_quotes = any('"' in line for line in lines)
            if not has_quotes:
                return {
                    "valid": False,
                    "error": "文件格式不正确，应该是CSV格式（字段用引号包围）"
                }
            
            # 尝试解析第一行（标题行）
            try:
                import io
                csv_reader = csv.reader(io.StringIO(lines[0]))
                headers = next(csv_reader)
                
                # 检查必要的列
                if not self._has_required_columns(headers):
                    return {
                        "valid": False,
                        "error": "缺少必要的列：分镜序号、文生图prompt"
                    }
                
            except Exception as e:
                return {
                    "valid": False,
                    "error": f"解析标题行失败: {str(e)}"
                }
            
            return {
                "valid": True,
                "message": "文件格式验证通过"
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"验证失败: {str(e)}"
            }
