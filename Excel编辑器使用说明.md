# Excel编辑器使用说明

## 功能概述

Excel编辑器是一个新增的模块，位于"角色参考图配置"模块的左边，提供了完整的Excel文件在线编辑功能。

## 主要功能

### 1. 文件上传
- 支持 `.xlsx` 和 `.xls` 格式的Excel文件
- 可以通过点击上传区域选择文件
- 支持拖拽文件到上传区域

### 2. 表格显示和编辑
- **自动渲染**：上传后自动将Excel内容渲染为可编辑的表格
- **列名显示**：第一行作为列名，其余行作为数据
- **智能列宽**：根据列名内容自动设置初始宽度
  - **分镜序号/镜头序号/序号/编号**：60px（最小宽度，适合1-20的数字）
  - **包含"prompt"或"提示词"或"文生图"**：400px（最大宽度，适合长文本）
  - **包含"URL"或"链接"**：200px（适合链接地址）
  - **包含"描述"或"说明"或"备注"**：250px（适合中等长度文本）
  - **其他列**：150px（默认宽度）

### 3. 编辑功能
- **单元格编辑**：点击任意单元格即可编辑内容
- **自动调整**：文本框会根据内容自动调整高度
- **特殊处理**：包含"prompt"或"提示词"的列使用更大的编辑框
- **实时保存**：编辑内容会实时保存到内存中

### 4. 尺寸调整功能
#### 列宽调整
- **拖拽调整**：每个列头右侧有调整手柄，可拖拽调整列宽
- **视觉反馈**：鼠标悬停时手柄会高亮显示
- **最小宽度**：每列最小宽度为30px
- **实时同步**：调整列宽时，该列所有单元格同步调整

#### 行高调整
- **拖拽调整**：每行底部有调整手柄，可拖拽调整行高
- **视觉反馈**：鼠标悬停时手柄会高亮显示
- **最小高度**：每行最小高度为30px
- **自动适应**：文本内容变化时，行高会自动适应

### 5. 导出功能
- **保持格式**：导出的Excel文件保持原有的列结构
- **包含修改**：导出文件包含所有编辑后的内容
- **自动下载**：点击导出按钮后自动下载文件

## 使用步骤

1. **切换到Excel编辑标签页**
   - 点击导航栏中的"Excel编辑"标签

2. **上传Excel文件**
   - 点击上传区域选择文件，或直接拖拽文件到上传区域
   - 支持的格式：.xlsx, .xls

3. **编辑内容**
   - 点击任意单元格开始编辑
   - 对于长文本（如prompt），编辑框会自动扩大
   - 可以拖拽列头右侧的调整手柄调整列宽
   - 可以拖拽行底部的调整手柄调整行高

4. **导出文件**
   - 编辑完成后，点击"导出Excel"按钮
   - 文件会自动下载到本地

5. **清空数据**
   - 如需重新开始，点击"清空数据"按钮

## 特色功能

### 智能列识别
- **序号列识别**：自动识别"分镜序号"、"镜头序号"等，设置最小宽度和居中对齐
- **提示词列识别**：自动识别包含"prompt"、"提示词"、"文生图"的列，提供最大编辑空间
- **链接列识别**：自动识别包含"URL"、"链接"的列，设置适中宽度
- **描述列识别**：自动识别包含"描述"、"说明"、"备注"的列，设置中等宽度
- **自动换行**：长文本自动换行显示

### 响应式设计
- 支持不同屏幕尺寸
- 移动设备上自动调整显示效果

### 数据保护
- 切换标签页时会检查是否有未保存的更改
- 提供确认对话框防止意外丢失数据

### 用户体验优化
- **实时进度提示**：上传和处理过程中显示进度
- **文件信息显示**：清晰显示文件名、行数、列数
- **直观操作反馈**：调整手柄高亮、拖拽光标变化
- **灵活调整**：所有列宽和行高都可以自由调整
- **智能适应**：根据内容类型自动设置合适的初始尺寸

## 注意事项

1. **文件格式**：仅支持Excel格式文件（.xlsx, .xls）
2. **数据保存**：编辑的内容仅保存在浏览器内存中，需要手动导出
3. **切换标签**：切换到其他标签页前会提醒保存更改
4. **文件大小**：建议上传的Excel文件不要过大，以确保良好的编辑体验

## 技术特点

- **前端渲染**：使用JavaScript动态生成可编辑表格
- **实时编辑**：无需刷新页面即可编辑内容
- **服务端处理**：使用pandas处理Excel文件读写
- **响应式布局**：适配不同设备屏幕

这个Excel编辑器模块完全独立于其他功能模块，不会影响现有的功能。
