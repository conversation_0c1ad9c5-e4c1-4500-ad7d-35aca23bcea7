import pandas as pd
import os
from typing import Dict, Any, List, Optional
from werkzeug.datastructures import FileStorage
import json

class ExcelProcessor:
    def __init__(self):
        self.required_columns = ["镜头序号", "文生图prompt"]
        self.scene_number_columns = ["镜头序号", "分镜序号"]
    
    def process_excel(self, file: FileStorage) -> List[Dict[str, Any]]:
        try:
            if file.filename.endswith('.xlsx'):
                df = pd.read_excel(file, engine='openpyxl')
            elif file.filename.endswith('.xls'):
                df = pd.read_excel(file, engine='xlrd')
            else:
                raise ValueError("Unsupported file format. Please use .xlsx or .xls files.")
            
            self._validate_columns(df)
            scene_number_col = self._get_scene_number_column(df)
            
            scenes = []
            for index, row in df.iterrows():
                scene_data = {
                    "id": str(index + 1),
                    "scene_number": str(row[scene_number_col]).strip(),
                    "prompt": str(row["文生图prompt"]).strip(),
                    "enabled": True,
                    "selected_characters": [],
                    "reference_text": "",
                    "image_count": 1,
                    "generated_images": []
                }
                scenes.append(scene_data)
            
            return scenes
            
        except Exception as e:
            raise Exception(f"Error processing Excel file: {str(e)}")
    
    def _validate_columns(self, df: pd.DataFrame):
        missing_columns = []
        
        # 检查文生图prompt列
        if "文生图prompt" not in df.columns:
            missing_columns.append("文生图prompt")
        
        # 检查镜头序号或分镜序号列
        scene_number_found = any(col in df.columns for col in self.scene_number_columns)
        if not scene_number_found:
            missing_columns.extend(self.scene_number_columns)
        
        if missing_columns:
            raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")
    
    def export_scenes_to_excel(self, scenes: List[Dict[str, Any]], filename: str = "exported_scenes.xlsx") -> str:
        try:
            if not scenes or len(scenes) == 0:
                raise Exception("No scenes data to export")
                
            data = []
            for scene in scenes:
                # 获取参考角色的URL信息
                selected_characters = scene.get("selected_characters", [])
                character_urls = []
                
                if selected_characters:
                    # 这里需要从字符管理器获取角色信息
                    try:
                        from .character_manager import CharacterManager
                        from .config_manager import ConfigManager
                        config_manager = ConfigManager()
                        character_manager = CharacterManager(config_manager)
                        
                        for char_id in selected_characters:
                            character = character_manager.get_character_by_id(char_id)
                            if character and character.get("url"):
                                character_urls.append(character["url"])
                    except Exception as char_error:
                        print(f"Warning: Could not get character URLs: {char_error}")
                
                # 基础场景信息 - 按用户要求的格式
                scene_data = {
                    "分镜": scene.get("scene_number", ""),
                    "文生图提示词": scene.get("prompt", ""),
                    "参考角色URL": "\n".join(character_urls) if character_urls else "",
                    "参考图说明": scene.get("reference_text", "")
                }
                
                data.append(scene_data)
            
            if not data:
                raise Exception("No valid scene data to export")
            
            df = pd.DataFrame(data)
            
            # 确保文件名有正确的扩展名
            if not filename.endswith('.xlsx'):
                filename = filename.replace('.xls', '') + '.xlsx'
            
            export_path = os.path.join(os.getcwd(), "exports", filename)
            os.makedirs(os.path.dirname(export_path), exist_ok=True)
            
            # 检查是否能导入openpyxl
            try:
                import openpyxl
                from openpyxl.styles import Alignment
            except ImportError:
                raise Exception("openpyxl library is required for Excel export. Please install it with: pip install openpyxl")
            
            # 创建Excel写入器，启用自动调整列宽
            with pd.ExcelWriter(export_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='分镜数据')
                
                # 获取工作表并调整列宽
                worksheet = writer.sheets['分镜数据']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 80)  # 增加最大宽度到80
                    worksheet.column_dimensions[column_letter].width = adjusted_width
                
                # 设置文本换行
                for row in worksheet.iter_rows():
                    for cell in row:
                        cell.alignment = Alignment(wrap_text=True, vertical='top')
            
            # 验证文件是否创建成功且不为空
            if not os.path.exists(export_path):
                raise Exception(f"Failed to create Excel file at {export_path}")
            
            file_size = os.path.getsize(export_path)
            if file_size == 0:
                raise Exception("Generated Excel file is empty")
            
            print(f"Excel file created successfully: {export_path}, size: {file_size} bytes")
            return export_path
            
        except Exception as e:
            print(f"Error in export_scenes_to_excel: {str(e)}")
            raise Exception(f"Error exporting scenes to Excel: {str(e)}")

    def export_excel(self, scenes: List[Dict[str, Any]], original_filename: str) -> str:
        try:
            data = []
            for scene in scenes:
                data.append({
                    "镜头序号": scene.get("scene_number", ""),
                    "文生图prompt": scene.get("prompt", "")
                })
            
            df = pd.DataFrame(data)
            
            base_name = os.path.splitext(original_filename)[0]
            export_filename = f"{base_name}_exported.xlsx"
            export_path = os.path.join(os.getcwd(), export_filename)
            
            df.to_excel(export_path, index=False, engine='openpyxl')
            
            return export_path
            
        except Exception as e:
            raise Exception(f"Error exporting Excel file: {str(e)}")
    
    def _get_scene_number_column(self, df: pd.DataFrame) -> str:
        """获取场景序号列名，优先使用镜头序号，其次使用分镜序号"""
        for col in self.scene_number_columns:
            if col in df.columns:
                return col
        raise ValueError("No scene number column found")

    def validate_excel_format(self, file_path: str) -> Dict[str, Any]:
        try:
            if file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path, engine='openpyxl')
            elif file_path.endswith('.xls'):
                df = pd.read_excel(file_path, engine='xlrd')
            else:
                return {
                    "valid": False,
                    "error": "Unsupported file format. Please use .xlsx or .xls files."
                }
            
            missing_columns = []
            
            # 检查文生图prompt列
            if "文生图prompt" not in df.columns:
                missing_columns.append("文生图prompt")
            
            # 检查场景序号列
            scene_number_found = any(col in df.columns for col in self.scene_number_columns)
            if not scene_number_found:
                missing_columns.extend(self.scene_number_columns)
            
            if missing_columns:
                return {
                    "valid": False,
                    "error": f"Missing required columns: {', '.join(missing_columns)}"
                }
            
            # 获取实际使用的场景序号列名
            scene_number_col = self._get_scene_number_column(df)
            
            empty_rows = []
            for index, row in df.iterrows():
                if pd.isna(row[scene_number_col]) or pd.isna(row["文生图prompt"]):
                    empty_rows.append(index + 1)
            
            warnings = []
            if empty_rows:
                warnings.append(f"Rows with empty data: {', '.join(map(str, empty_rows))}")
            
            return {
                "valid": True,
                "row_count": len(df),
                "warnings": warnings
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"Error validating Excel file: {str(e)}"
            }

    def read_excel_for_editing(self, file: FileStorage) -> Dict[str, Any]:
        """读取Excel文件用于编辑，返回完整的表格数据"""
        try:
            if file.filename.endswith('.xlsx'):
                df = pd.read_excel(file, engine='openpyxl')
            elif file.filename.endswith('.xls'):
                df = pd.read_excel(file, engine='xlrd')
            else:
                raise ValueError("Unsupported file format. Please use .xlsx or .xls files.")

            # 将DataFrame转换为可编辑的格式
            columns = df.columns.tolist()
            rows = []

            for index, row in df.iterrows():
                row_data = []
                for col in columns:
                    cell_value = row[col]
                    # 处理NaN值
                    if pd.isna(cell_value):
                        cell_value = ""
                    else:
                        cell_value = str(cell_value)
                    row_data.append(cell_value)
                rows.append(row_data)

            return {
                "success": True,
                "filename": file.filename,
                "columns": columns,
                "rows": rows,
                "row_count": len(rows),
                "column_count": len(columns)
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Error reading Excel file: {str(e)}"
            }

    def save_edited_excel(self, data: Dict[str, Any], filename: str) -> str:
        """保存编辑后的Excel数据"""
        try:
            columns = data.get("columns", [])
            rows = data.get("rows", [])

            if not columns or not rows:
                raise ValueError("No data to save")

            # 创建DataFrame
            df = pd.DataFrame(rows, columns=columns)

            # 确保文件名有正确的扩展名
            if not filename.endswith('.xlsx'):
                filename = filename.replace('.xls', '') + '.xlsx'

            export_path = os.path.join(os.getcwd(), "exports", filename)
            os.makedirs(os.path.dirname(export_path), exist_ok=True)

            # 检查是否能导入openpyxl
            try:
                import openpyxl
                from openpyxl.styles import Alignment
            except ImportError:
                raise Exception("openpyxl library is required for Excel export. Please install it with: pip install openpyxl")

            # 创建Excel写入器
            with pd.ExcelWriter(export_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Sheet1')

                # 获取工作表并调整列宽
                worksheet = writer.sheets['Sheet1']

                # 根据列名和内容调整列宽
                for i, column in enumerate(worksheet.columns):
                    column_letter = column[0].column_letter
                    column_name = columns[i] if i < len(columns) else ""

                    # 计算最大内容长度
                    max_length = len(column_name)
                    for row in rows:
                        if i < len(row):
                            cell_length = len(str(row[i]))
                            if cell_length > max_length:
                                max_length = cell_length

                    # 特殊处理包含"prompt"或"提示词"的列
                    if "prompt" in column_name.lower() or "提示词" in column_name:
                        adjusted_width = min(max(max_length + 2, 50), 100)  # 最小50，最大100
                    else:
                        adjusted_width = min(max_length + 2, 50)  # 其他列最大50

                    worksheet.column_dimensions[column_letter].width = adjusted_width

                # 设置文本换行和垂直对齐
                for row in worksheet.iter_rows():
                    for cell in row:
                        cell.alignment = Alignment(wrap_text=True, vertical='top')

            # 验证文件是否创建成功
            if not os.path.exists(export_path):
                raise Exception(f"Failed to create Excel file at {export_path}")

            file_size = os.path.getsize(export_path)
            if file_size == 0:
                raise Exception("Generated Excel file is empty")

            return export_path

        except Exception as e:
            raise Exception(f"Error saving Excel file: {str(e)}")