import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

class Logger:
    def __init__(self, log_file: str = "log.txt", max_recent_logs: int = 100):
        self.log_file = log_file
        self.max_recent_logs = max_recent_logs
        self.recent_logs = []
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def log_api_call_start(self, url: str, headers: Dict[str, Any], payload: Dict[str, Any],
                          trace_id: str, api_call_id: str = None):
        """记录API调用开始信息"""
        if api_call_id is None:
            import uuid
            api_call_id = str(uuid.uuid4())[:8]

        # 构建完整的调用标识
        call_identifier = f"{trace_id}_api_{api_call_id}"

        # 记录请求信息
        request_info = f"""
=== API调用开始 [{call_identifier}] ===
URL: {url}
请求头: {json.dumps(headers, ensure_ascii=False, indent=2)}
请求载荷: {json.dumps(payload, ensure_ascii=False, indent=2)}
"""
        self.log(request_info, "INFO", trace_id)
        return api_call_id

    def log_api_call_end(self, url: str, response_status: int, response_data: Any,
                        trace_id: str, duration: float, api_call_id: str):
        """记录API调用结束信息"""
        # 构建完整的调用标识
        call_identifier = f"{trace_id}_api_{api_call_id}"

        # 记录响应信息
        if isinstance(response_data, dict):
            response_text = json.dumps(response_data, ensure_ascii=False, indent=2)
        else:
            response_text = str(response_data)

        # 安全处理duration格式化
        duration_text = f"{duration:.2f}秒" if duration is not None else "未知"

        response_info = f"""
=== API调用响应 [{call_identifier}] ===
状态码: {response_status}
响应时间: {duration_text}
响应内容: {response_text[:2000]}{"..." if len(response_text) > 2000 else ""}
=== API调用结束 [{call_identifier}] ===
"""
        self.log(response_info, "INFO", trace_id)

    def log_detailed_api_call(self, url: str, headers: Dict[str, Any], payload: Dict[str, Any],
                             response_status: int, response_data: Any, trace_id: str, duration: float = None,
                             api_call_id: str = None):
        """记录完整的API调用信息（兼容旧接口）"""
        if api_call_id is None:
            import uuid
            api_call_id = str(uuid.uuid4())[:8]

        # 只记录完整的调用信息，不重复记录
        call_identifier = f"{trace_id}_api_{api_call_id}"

        # 记录响应信息
        if isinstance(response_data, dict):
            response_text = json.dumps(response_data, ensure_ascii=False, indent=2)
        else:
            response_text = str(response_data)

        # 安全处理duration格式化
        duration_text = f"{duration:.2f}秒" if duration is not None else "未知"

        response_info = f"""
=== API调用响应 [{call_identifier}] ===
状态码: {response_status}
响应时间: {duration_text}
响应内容: {response_text[:2000]}{"..." if len(response_text) > 2000 else ""}
=== API调用结束 [{call_identifier}] ===
"""
        self.log(response_info, "INFO", trace_id)
        return api_call_id

    def log(self, message: str, level: str = "INFO", trace_id: Optional[str] = None):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if trace_id:
            formatted_message = f"[{trace_id}] {message}"
        else:
            formatted_message = message
        
        log_entry = {
            "timestamp": timestamp,
            "level": level,
            "message": formatted_message,
            "trace_id": trace_id
        }
        
        self.recent_logs.append(log_entry)
        if len(self.recent_logs) > self.max_recent_logs:
            self.recent_logs.pop(0)
        
        if level == "DEBUG":
            self.logger.debug(formatted_message)
        elif level == "INFO":
            self.logger.info(formatted_message)
        elif level == "WARNING":
            self.logger.warning(formatted_message)
        elif level == "ERROR":
            self.logger.error(formatted_message)
        elif level == "CRITICAL":
            self.logger.critical(formatted_message)
        else:
            self.logger.info(formatted_message)
    
    def log_api_call(self, url: str, payload: Dict[str, Any], response: Dict[str, Any], 
                     trace_id: str, duration: float = None):
        api_log = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "trace_id": trace_id,
            "type": "API_CALL",
            "url": url,
            "request_payload": payload,
            "response": response,
            "duration_seconds": duration
        }
        
        log_message = f"API Call - URL: {url}, Duration: {duration}s" if duration else f"API Call - URL: {url}"
        self.log(log_message, "INFO", trace_id)
        
        try:
            with open(f"api_log_{trace_id}.json", 'w', encoding='utf-8') as f:
                json.dump(api_log, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log(f"Failed to save API log: {str(e)}", "ERROR", trace_id)
    
    def log_excel_processing(self, filepath: str, scene_count: int, trace_id: str):
        self.log(f"Excel processing - File: {filepath}, Scenes: {scene_count}", "INFO", trace_id)
    
    def log_image_generation(self, scene_number: str, image_count: int, 
                           success: bool, trace_id: str, error: str = None):
        if success:
            self.log(f"Image generation success - Scene: {scene_number}, Images: {image_count}", "INFO", trace_id)
        else:
            self.log(f"Image generation failed - Scene: {scene_number}, Error: {error}", "ERROR", trace_id)
    
    def log_batch_progress(self, current: int, total: int, trace_id: str):
        percentage = (current / total) * 100 if total > 0 else 0
        self.log(f"Batch progress: {current}/{total} ({percentage:.1f}%)", "INFO", trace_id)
    
    def get_recent_logs(self, count: Optional[int] = None) -> List[Dict[str, Any]]:
        if count is None:
            return self.recent_logs.copy()
        else:
            return self.recent_logs[-count:] if count > 0 else []
    
    def get_logs_by_trace_id(self, trace_id: str) -> List[Dict[str, Any]]:
        return [log for log in self.recent_logs if log.get("trace_id") == trace_id]
    
    def clear_recent_logs(self):
        self.recent_logs.clear()
    
    def export_logs(self, filename: str = None) -> str:
        if filename is None:
            filename = f"logs_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.recent_logs, f, ensure_ascii=False, indent=2)
            return filename
        except Exception as e:
            self.log(f"Failed to export logs: {str(e)}", "ERROR")
            raise e
    
    def get_log_stats(self) -> Dict[str, Any]:
        if not self.recent_logs:
            return {"total": 0, "by_level": {}}
        
        stats = {"total": len(self.recent_logs), "by_level": {}}
        
        for log in self.recent_logs:
            level = log.get("level", "UNKNOWN")
            stats["by_level"][level] = stats["by_level"].get(level, 0) + 1
        
        return stats